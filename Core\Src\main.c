#include "gd32f470vet6_bsp.h"

uint8_t rs485_rxbuffer[512];
uint8_t rs485_dma_buffer[512];
volatile uint8_t rs485_rx_flag = 0;


void USART1_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE)){
        /* clear IDLE flag */
        ucLed[5] = ucLed[5]  ? 0 : 1;
        usart_data_receive(USART1);
        
        /* number of data received */
        uint16_t rx_len = 512 - dma_transfer_number_get(DMA0, DMA_CH5);
        if(rx_len > 0) {
            memcpy(rs485_dma_buffer, rs485_rxbuffer, rx_len);
            memset(rs485_rxbuffer, 0, 512);
            rs485_rx_flag = 1;
        }
        
        /* disable DMA and reconfigure */
        dma_channel_disable(DMA0, DMA_CH5);
        dma_flag_clear(DMA0, DMA_CH5, DMA_FLAG_FTF);
        dma_transfer_number_config(DMA0, DMA_CH5, 512);
        dma_channel_enable(DMA0, DMA_CH5);
    }
}

// RS485 DMA接收初始化
void rs485_dma_init(void)
{
    dma_single_data_parameter_struct dma_init_struct;
    
    /* 使能DMA时钟 */
    rcu_periph_clock_enable(RCU_DMA0);
    
    /* 配置DMA接收通道 */
    dma_deinit(DMA0, DMA_CH5);
    dma_init_struct.periph_addr         = (uint32_t)&USART_DATA(USART1);
    dma_init_struct.memory0_addr        = (uint32_t)rs485_rxbuffer;
    dma_init_struct.direction           = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;  // 统一宽度字段
    dma_init_struct.priority            = DMA_PRIORITY_ULTRA_HIGH;
    dma_init_struct.number              = 512;
    dma_init_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.circular_mode       = DMA_CIRCULAR_MODE_DISABLE;
    dma_single_data_mode_init(DMA0, DMA_CH5, &dma_init_struct);
    
    /* 配置DMA通道 */
    dma_channel_subperipheral_select(DMA0, DMA_CH5, DMA_SUBPERI4);
    dma_channel_enable(DMA0, DMA_CH5);
    
    /* 使能USART DMA接收 */
    usart_dma_receive_config(USART1, USART_RECEIVE_DMA_ENABLE);
}

// RS485中断初始化
void rs485_interrupt_init(void)
{
    /* 使能USART1中断 */
    nvic_irq_enable(USART1_IRQn, 1, 0);
    
    /* 使能IDLE中断 */
    usart_interrupt_enable(USART1, USART_INT_IDLE);
}

void rs485_set_tx_mode(void)
{
    gpio_bit_set(USART1_GPIO_PORT, USART1_CS_PIN);  // PA1=1，发送模式
}

void rs485_set_rx_mode(void)
{
    gpio_bit_reset(USART1_GPIO_PORT, USART1_CS_PIN);  // PA1=0，接收模式
}

// RS485发送函数
int rs485_printf(const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    
    // 格式化字符串
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    // 切换到发送模式
    rs485_set_tx_mode();
    //delay_1ms(1);  // 等待切换完成
    
    // 发送数据
    for(int i = 0; i < len; i++){
        usart_data_transmit(USART1, buffer[i]);
        while(RESET == usart_flag_get(USART1, USART_FLAG_TBE));
    }
    
    // 等待发送完成
    while(RESET == usart_flag_get(USART1, USART_FLAG_TC));
    //delay_1ms(1);
    
    // 切换回接收模式
    rs485_set_rx_mode();
    
    return len;
}

// RS485发送固定字符串
void rs485_send_string(const char *str)
{
    rs485_set_tx_mode();
    //delay_1ms(1);
    
    while(*str) {
        usart_data_transmit(USART1, *str);
        while(RESET == usart_flag_get(USART1, USART_FLAG_TBE));
        str++;
    }
    
    while(RESET == usart_flag_get(USART1, USART_FLAG_TC));
    //delay_1ms(1);
    rs485_set_rx_mode();
}

// RS485接收处理
void rs485_task(void)
{
    static int counter = 0;

    // 简单计数器，每次调用就加1
    counter++;
ucLed[2] = ucLed[2] ? 0 : 1;  // 改为TOGGLE，这样LED会闪烁
    // 每10次调用闪烁一次LED3 - 这样LED3会快速闪烁表示任务在运行
    if(counter >= 10) {
        counter = 0;
        ucLed[2] = ucLed[2] ? 0 : 1;  // 改为TOGGLE，这样LED会闪烁
    }

    if(rs485_rx_flag) {
        rs485_rx_flag = 0;

        // 简单回显测试
        rs485_printf("RS485: %s", rs485_dma_buffer);

        // 清空缓冲区
        memset(rs485_dma_buffer, 0, sizeof(rs485_dma_buffer));
    }
}


void bsp_rs485_init(void)
{
    // 使能时钟
    rcu_periph_clock_enable(USART1_GPIO_CLK);
    rcu_periph_clock_enable(RCU_USART1);
    
    // 配置TX/RX引脚 (PA2/PA3)
    gpio_af_set(USART1_GPIO_PORT, GPIO_AF_7, USART1_TX_PIN);
    gpio_af_set(USART1_GPIO_PORT, GPIO_AF_7, USART1_RX_PIN);
    
    gpio_mode_set(USART1_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, USART1_TX_PIN);
    gpio_output_options_set(USART1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, USART1_RX_PIN);
    
    gpio_mode_set(USART1_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, USART1_RX_PIN);
    
    // 配置DE/RE控制引脚 (PA1)
    gpio_mode_set(USART1_GPIO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, USART1_CS_PIN);
    gpio_output_options_set(USART1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, USART1_CS_PIN);
    
    // 配置USART1
    usart_deinit(USART1);
    usart_baudrate_set(USART1, 115200U);  // 修改为115200波特率
    usart_word_length_set(USART1, USART_WL_8BIT);
    usart_stop_bit_set(USART1, USART_STB_1BIT);
    usart_parity_config(USART1, USART_PM_NONE);
    usart_receive_config(USART1, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART1, USART_TRANSMIT_ENABLE);
    usart_enable(USART1);
    
		//初始化DMA接收
    rs485_dma_init();
		
		//初始化接收中断
    rs485_interrupt_init();
		
    // 默认接收模式
    rs485_set_rx_mode();
    
}

// void Usart1PeriphInit(void)
// {
//     rcu_periph_clock_enable(USART1_GPIO_CLK);
//     rcu_periph_clock_enable(RCU_USART1);

//     gpio_af_set(USART1_GPIO_PORT, GPIO_AF_7, USART1_TX_PIN);
//     gpio_af_set(USART1_GPIO_PORT, GPIO_AF_7, USART1_RX_PIN);

//     gpio_mode_set(USART1_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, USART1_TX_PIN);
//     gpio_output_options_set(USART1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, USART1_TX_PIN);
//     gpio_mode_set(USART1_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, USART1_RX_PIN);
//     gpio_output_options_set(USART1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, USART1_RX_PIN);

//     gpio_mode_set(USART1_GPIO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, USART1_CS_PIN);
//     gpio_output_options_set(USART1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, USART1_CS_PIN);

//     usart_deinit(USART1);
//     usart_baudrate_set(USART1, USART1_BAUDRATE);
//     usart_transmit_config(USART1, USART_TRANSMIT_ENABLE);
//     usart_receive_config(USART1, USART_RECEIVE_ENABLE);
//     usart_enable(USART1);

//     nvic_irq_enable(USART1_IRQn, 2, 0);
//     usart_interrupt_enable(USART1, USART_INT_RBNE);

//     gpio_bit_reset(USART1_GPIO_PORT, USART1_CS_PIN);
// }

// void USART1_IRQHandler(void)
// {
//     uint8_t res;
//     if (RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_RBNE))
//     {
//         usart_flag_clear(USART1, USART_FLAG_RBNE);
//         ucLed[5] = ucLed[5] ? 0 : 1;
//         res = usart_data_receive(USART1);
        
//         // 切换到发送模式
//         gpio_bit_set(USART1_GPIO_PORT, USART1_CS_PIN);
//         delay_1ms(1);  // 给RS485芯片切换时间

//         // 发送数据
//         usart_data_transmit(USART1, res);
        
//         // 等待发送缓冲区空
//         uint32_t timeout_ms = uwTick;
//         while (RESET == usart_flag_get(USART1, USART_FLAG_TBE))
//         {
//             if ((uwTick - timeout_ms) > 50) {  // 增加到50ms
//                 gpio_bit_reset(USART1_GPIO_PORT, USART1_CS_PIN);
//                 return;
//             }
//         }

//         // 等待传输完成
//         timeout_ms = uwTick;
//         while (RESET == usart_flag_get(USART1, USART_FLAG_TC))
//         {
//             if ((uwTick - timeout_ms) > 50) {  // 增加到50ms
//                 gpio_bit_reset(USART1_GPIO_PORT, USART1_CS_PIN);
//                 return;
//             }
//         }
        
//         // 清除TC标志并切换回接收模式
//         usart_flag_clear(USART1, USART_FLAG_TC);
//         delay_1ms(1);  // 给RS485芯片切换时间
// //        gpio_bit_reset(USART1_GPIO_PORT, USART1_CS_PIN);
//     }
// }
int main(void)
{
    SysInit();

    // TfCardTest();
    // Usart1PeriphInit();
    bsp_rs485_init();
    while(1)
    {
        // ucLed[0] = ucLed[0] ? 0 : 1;
        LedTask();
        rs485_task();
        delay_1ms(500);
        // TaskExeution();
    }
}
